package com.netty.socket;

import com.netty.socket.manager.SessionManager;
import com.netty.socket.service.MessageRoutingService;
import com.netty.socket.service.NettyServerService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * Netty Socket服务器应用测试类
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
class NettySocketServerApplicationTests {
    
    @Autowired
    private NettyServerService nettyServerService;
    
    @Autowired
    private SessionManager sessionManager;
    
    @Autowired
    private MessageRoutingService messageRoutingService;
    
    /**
     * 测试Spring上下文加载
     */
    @Test
    void contextLoads() {
        assertNotNull(nettyServerService);
        assertNotNull(sessionManager);
        assertNotNull(messageRoutingService);
    }
    
    /**
     * 测试Netty服务器服务
     */
    @Test
    void testNettyServerService() {
        assertNotNull(nettyServerService);
        // 注意：在测试环境中不启动实际的Netty服务器
        // 可以测试配置是否正确加载
    }
    
    /**
     * 测试会话管理器
     */
    @Test
    void testSessionManager() {
        assertNotNull(sessionManager);
        // 可以测试会话管理的基本功能
        assertNotNull(sessionManager.getSessionStatistics());
    }
    
    /**
     * 测试消息路由服务
     */
    @Test
    void testMessageRoutingService() {
        assertNotNull(messageRoutingService);
        // 可以测试消息队列统计信息
        assertNotNull(messageRoutingService.getQueueStats());
    }
}
