# 测试环境配置
server:
  port: 0  # 随机端口

# Netty Socket 服务器配置（测试环境）
netty:
  socket:
    primary-port: 0  # 测试时不启动实际端口
    additional-ports: []
    boss-thread-count: 1
    worker-thread-count: 2
    heartbeat:
      interval: 10
      timeout: 30
      max-failures: 2

# 连接池配置（测试环境）
connection:
  pool:
    max-connections: 100
    idle-timeout: 5
    cleanup-interval: 1

# 消息路由配置（测试环境）
message:
  routing:
    queue-size: 100
    thread-pool-size: 2
    timeout: 10

# 会话管理配置（测试环境）
session:
  timeout: 10
  cleanup-interval: 1
  max-sessions: 100

# 日志配置（测试环境）
logging:
  level:
    com.netty.socket: DEBUG
    org.springframework: WARN
    io.netty: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Spring Boot Actuator 监控配置（测试环境）
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
