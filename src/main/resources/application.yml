# Netty Socket 服务器配置
server:
  port: 8080  # Spring Boot Web 端口
  servlet:
    context-path: /api

# Netty Socket 服务器配置
netty:
  socket:
    # 主端口配置
    primary-port: 9999
    # 扩展端口配置（支持多端口负载均衡）
    additional-ports: 
      - 10000
      - 10001
      - 10002
    # Boss 线程组大小（接受连接的线程数）
    boss-thread-count: 1
    # Worker 线程组大小（处理I/O的线程数）
    worker-thread-count: 8
    # 连接超时时间（毫秒）
    connect-timeout: 30000
    # 保持连接活跃
    keep-alive: true
    # TCP_NODELAY 设置
    tcp-no-delay: true
    # SO_BACKLOG 设置
    so-backlog: 1024
    # SO_RCVBUF 设置
    so-rcv-buf: 65536
    # SO_SNDBUF 设置
    so-snd-buf: 65536
    # 心跳检测配置
    heartbeat:
      # 心跳间隔（秒）
      interval: 30
      # 心跳超时时间（秒）
      timeout: 90
      # 最大失败次数
      max-failures: 3

# 连接池配置
connection:
  pool:
    # 最大连接数
    max-connections: 10000
    # 连接空闲超时时间（分钟）
    idle-timeout: 30
    # 清理间隔（分钟）
    cleanup-interval: 5

# 消息路由配置
message:
  routing:
    # 消息队列大小
    queue-size: 1000
    # 消息处理线程池大小
    thread-pool-size: 16
    # 消息超时时间（秒）
    timeout: 30

# 会话管理配置
session:
  # 会话超时时间（分钟）
  timeout: 60
  # 会话清理间隔（分钟）
  cleanup-interval: 10
  # 最大会话数
  max-sessions: 50000

# 日志配置
logging:
  level:
    com.netty.socket: DEBUG
    io.netty: INFO
    org.springframework: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/netty-socket-server.log
    max-size: 100MB
    max-history: 30

# Spring Boot Actuator 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 应用信息
info:
  app:
    name: Netty Socket Server
    description: 基于Spring Boot 3.4和Netty的高性能Socket服务框架
    version: 1.0.0
    java-version: 17

# 开发环境配置
---
spring:
  config:
    activate:
      on-profile: dev
      
netty:
  socket:
    primary-port: 9999
    worker-thread-count: 4

logging:
  level:
    com.netty.socket: DEBUG
    root: INFO

# 生产环境配置
---
spring:
  config:
    activate:
      on-profile: prod
      
netty:
  socket:
    primary-port: 9999
    worker-thread-count: 16

logging:
  level:
    com.netty.socket: INFO
    root: WARN
  file:
    name: /var/log/netty-socket-server/application.log
