package com.netty.socket.service;

import com.netty.socket.config.NettyProperties;
import com.netty.socket.handler.MessageDecoder;
import com.netty.socket.handler.MessageEncoder;
import com.netty.socket.handler.SocketServerHandler;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.IdleStateHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Netty服务器服务
 * 负责启动和管理Netty服务器，支持多端口监听
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@Service
public class NettyServerService {
    
    private static final Logger logger = LoggerFactory.getLogger(NettyServerService.class);
    
    @Autowired
    private NettyProperties nettyProperties;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    /**
     * Boss线程组（用于接受连接）
     */
    private EventLoopGroup bossGroup;
    
    /**
     * Worker线程组（用于处理I/O）
     */
    private EventLoopGroup workerGroup;
    
    /**
     * 服务器Channel列表
     */
    private List<Channel> serverChannels = new ArrayList<>();
    
    /**
     * 服务器运行状态
     */
    private volatile boolean running = false;
    
    @PostConstruct
    public void init() {
        logger.info("Netty服务器服务初始化...");
    }
    
    /**
     * 启动Netty服务器
     */
    public void startServer() {
        if (running) {
            logger.warn("Netty服务器已经在运行中");
            return;
        }
        
        try {
            // 创建线程组
            bossGroup = new NioEventLoopGroup(nettyProperties.getBossThreadCount());
            workerGroup = new NioEventLoopGroup(nettyProperties.getWorkerThreadCount());
            
            // 启动主端口
            startServerOnPort(nettyProperties.getPrimaryPort());
            
            // 启动扩展端口
            if (nettyProperties.getAdditionalPorts() != null) {
                for (Integer port : nettyProperties.getAdditionalPorts()) {
                    startServerOnPort(port);
                }
            }
            
            running = true;
            logger.info("Netty服务器启动成功，监听端口: 主端口={}, 扩展端口={}", 
                nettyProperties.getPrimaryPort(), nettyProperties.getAdditionalPorts());
                
        } catch (Exception e) {
            logger.error("Netty服务器启动失败: {}", e.getMessage(), e);
            stopServer();
            throw new RuntimeException("Netty服务器启动失败", e);
        }
    }
    
    /**
     * 在指定端口启动服务器
     * 
     * @param port 端口号
     */
    private void startServerOnPort(int port) {
        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                .channel(NioServerSocketChannel.class)
                .option(ChannelOption.SO_BACKLOG, nettyProperties.getSoBacklog())
                .option(ChannelOption.SO_REUSEADDR, true)
                .childOption(ChannelOption.SO_KEEPALIVE, nettyProperties.isKeepAlive())
                .childOption(ChannelOption.TCP_NODELAY, nettyProperties.isTcpNoDelay())
                .childOption(ChannelOption.SO_RCVBUF, nettyProperties.getSoRcvBuf())
                .childOption(ChannelOption.SO_SNDBUF, nettyProperties.getSoSndBuf())
                .childOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, nettyProperties.getConnectTimeout())
                .handler(new LoggingHandler(LogLevel.INFO))
                .childHandler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) throws Exception {
                        ChannelPipeline pipeline = ch.pipeline();
                        
                        // 添加空闲状态处理器（心跳检测）
                        pipeline.addLast("idleStateHandler", new IdleStateHandler(
                            nettyProperties.getHeartbeat().getTimeout(), 0, 0, TimeUnit.SECONDS));
                        
                        // 添加日志处理器
                        pipeline.addLast("loggingHandler", new LoggingHandler(LogLevel.DEBUG));
                        
                        // 添加消息编解码器
                        pipeline.addLast("messageDecoder", new MessageDecoder());
                        pipeline.addLast("messageEncoder", new MessageEncoder());
                        
                        // 添加业务处理器
                        pipeline.addLast("socketServerHandler", getSocketServerHandler());
                    }
                });
            
            // 绑定端口并启动服务器
            ChannelFuture future = bootstrap.bind(port).sync();
            
            if (future.isSuccess()) {
                Channel serverChannel = future.channel();
                serverChannels.add(serverChannel);
                logger.info("Netty服务器在端口 {} 启动成功", port);
            } else {
                logger.error("Netty服务器在端口 {} 启动失败", port);
                throw new RuntimeException("端口绑定失败: " + port);
            }
            
        } catch (Exception e) {
            logger.error("在端口 {} 启动服务器时发生异常: {}", port, e.getMessage(), e);
            throw new RuntimeException("端口启动失败: " + port, e);
        }
    }
    
    /**
     * 获取Socket服务器处理器实例
     * 使用Spring容器管理，确保依赖注入正常工作
     */
    private SocketServerHandler getSocketServerHandler() {
        return applicationContext.getBean(SocketServerHandler.class);
    }
    
    /**
     * 停止Netty服务器
     */
    public void stopServer() {
        if (!running) {
            logger.warn("Netty服务器未在运行");
            return;
        }
        
        try {
            running = false;
            
            // 关闭所有服务器Channel
            for (Channel serverChannel : serverChannels) {
                if (serverChannel != null && serverChannel.isActive()) {
                    serverChannel.close().sync();
                }
            }
            serverChannels.clear();
            
            // 关闭线程组
            if (workerGroup != null) {
                workerGroup.shutdownGracefully().sync();
            }
            if (bossGroup != null) {
                bossGroup.shutdownGracefully().sync();
            }
            
            logger.info("Netty服务器已停止");
            
        } catch (Exception e) {
            logger.error("停止Netty服务器时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 重启Netty服务器
     */
    public void restartServer() {
        logger.info("重启Netty服务器...");
        stopServer();
        
        // 等待一段时间确保资源完全释放
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        startServer();
    }
    
    /**
     * 获取服务器运行状态
     * 
     * @return 是否正在运行
     */
    public boolean isRunning() {
        return running;
    }
    
    /**
     * 获取服务器统计信息
     * 
     * @return 统计信息
     */
    public ServerStats getServerStats() {
        int activeChannels = 0;
        for (Channel serverChannel : serverChannels) {
            if (serverChannel != null && serverChannel.isActive()) {
                activeChannels++;
            }
        }
        
        return new ServerStats(
            running,
            serverChannels.size(),
            activeChannels,
            nettyProperties.getPrimaryPort(),
            nettyProperties.getAdditionalPorts()
        );
    }
    
    @PreDestroy
    public void destroy() {
        logger.info("Netty服务器服务正在关闭...");
        stopServer();
    }
    
    /**
     * 服务器统计信息
     */
    public static class ServerStats {
        private final boolean running;
        private final int totalChannels;
        private final int activeChannels;
        private final int primaryPort;
        private final List<Integer> additionalPorts;
        
        public ServerStats(boolean running, int totalChannels, int activeChannels, 
                          int primaryPort, List<Integer> additionalPorts) {
            this.running = running;
            this.totalChannels = totalChannels;
            this.activeChannels = activeChannels;
            this.primaryPort = primaryPort;
            this.additionalPorts = additionalPorts;
        }
        
        // Getter 方法
        public boolean isRunning() { return running; }
        public int getTotalChannels() { return totalChannels; }
        public int getActiveChannels() { return activeChannels; }
        public int getPrimaryPort() { return primaryPort; }
        public List<Integer> getAdditionalPorts() { return additionalPorts; }
        
        @Override
        public String toString() {
            return String.format("ServerStats{running=%s, totalChannels=%d, activeChannels=%d, primaryPort=%d, additionalPorts=%s}", 
                running, totalChannels, activeChannels, primaryPort, additionalPorts);
        }
    }
}
