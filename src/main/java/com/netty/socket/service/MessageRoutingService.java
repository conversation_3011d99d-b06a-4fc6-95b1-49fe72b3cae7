package com.netty.socket.service;

import com.netty.socket.config.MessageRoutingProperties;
import com.netty.socket.manager.SessionManager;
import com.netty.socket.model.SocketMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.*;

/**
 * 消息路由服务
 * 负责处理和路由客户端发送的消息，支持点对点消息、广播消息等
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@Service
public class MessageRoutingService {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageRoutingService.class);
    
    @Autowired
    private MessageRoutingProperties messageRoutingProperties;
    
    @Autowired
    private SessionManager sessionManager;
    
    @Autowired
    private MessageProcessingService messageProcessingService;
    
    /**
     * 消息处理线程池
     */
    private ThreadPoolExecutor messageProcessingExecutor;
    
    /**
     * 消息队列
     */
    private BlockingQueue<SocketMessage> messageQueue;
    
    /**
     * 消息处理任务运行标志
     */
    private volatile boolean running = false;
    
    @PostConstruct
    public void init() {
        // 初始化消息队列
        messageQueue = new LinkedBlockingQueue<>(messageRoutingProperties.getQueueSize());
        
        // 初始化线程池
        messageProcessingExecutor = new ThreadPoolExecutor(
            messageRoutingProperties.getThreadPoolSize(),
            messageRoutingProperties.getThreadPoolSize(),
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(),
            new ThreadFactory() {
                private int counter = 0;
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "MessageRouting-" + (++counter));
                    thread.setDaemon(true);
                    return thread;
                }
            }
        );
        
        // 启动消息处理任务
        running = true;
        for (int i = 0; i < messageRoutingProperties.getThreadPoolSize(); i++) {
            messageProcessingExecutor.submit(new MessageProcessingTask());
        }
        
        logger.info("消息路由服务初始化完成: 队列大小={}, 线程池大小={}, 超时时间={}秒", 
            messageRoutingProperties.getQueueSize(), 
            messageRoutingProperties.getThreadPoolSize(),
            messageRoutingProperties.getTimeout());
    }
    
    @PreDestroy
    public void destroy() {
        running = false;
        
        if (messageProcessingExecutor != null) {
            messageProcessingExecutor.shutdown();
            try {
                if (!messageProcessingExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    messageProcessingExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                messageProcessingExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        logger.info("消息路由服务已关闭");
    }
    
    /**
     * 路由消息
     * 
     * @param message 要路由的消息
     * @return 是否成功加入处理队列
     */
    public boolean routeMessage(SocketMessage message) {
        if (message == null) {
            logger.warn("尝试路由空消息");
            return false;
        }
        
        try {
            // 将消息加入处理队列
            boolean offered = messageQueue.offer(message, 1, TimeUnit.SECONDS);
            
            if (offered) {
                logger.debug("消息已加入路由队列: messageId={}, sessionId={}, type={}", 
                    message.getMessageId(), message.getSessionId(), message.getType());
            } else {
                logger.warn("消息队列已满，消息路由失败: messageId={}, sessionId={}", 
                    message.getMessageId(), message.getSessionId());
            }
            
            return offered;
        } catch (InterruptedException e) {
            logger.error("消息路由被中断: messageId={}, sessionId={}", 
                message.getMessageId(), message.getSessionId(), e);
            Thread.currentThread().interrupt();
            return false;
        }
    }
    
    /**
     * 发送点对点消息
     * 
     * @param targetSessionId 目标会话ID
     * @param message 消息内容
     * @return 是否发送成功
     */
    public boolean sendToSession(String targetSessionId, SocketMessage message) {
        if (targetSessionId == null || message == null) {
            logger.warn("发送点对点消息参数无效: targetSessionId={}, message={}", targetSessionId, message);
            return false;
        }
        
        message.setReceiverId(targetSessionId);
        return sessionManager.sendMessageToSession(targetSessionId, message);
    }
    
    /**
     * 发送消息给指定用户的所有会话
     * 
     * @param targetUserId 目标用户ID
     * @param message 消息内容
     * @return 成功发送的会话数量
     */
    public int sendToUser(String targetUserId, SocketMessage message) {
        if (targetUserId == null || message == null) {
            logger.warn("发送用户消息参数无效: targetUserId={}, message={}", targetUserId, message);
            return 0;
        }
        
        message.setReceiverId(targetUserId);
        return sessionManager.sendMessageToUser(targetUserId, message);
    }
    
    /**
     * 广播消息到所有会话
     * 
     * @param message 消息内容
     * @return 成功发送的会话数量
     */
    public int broadcastMessage(SocketMessage message) {
        if (message == null) {
            logger.warn("广播消息参数无效");
            return 0;
        }
        
        return sessionManager.broadcastMessage(message);
    }
    
    /**
     * 获取消息队列统计信息
     * 
     * @return 统计信息
     */
    public MessageQueueStats getQueueStats() {
        return new MessageQueueStats(
            messageQueue.size(),
            messageQueue.remainingCapacity(),
            messageProcessingExecutor.getActiveCount(),
            messageProcessingExecutor.getCompletedTaskCount(),
            messageProcessingExecutor.getTaskCount()
        );
    }
    
    /**
     * 消息处理任务
     */
    private class MessageProcessingTask implements Runnable {
        @Override
        public void run() {
            logger.info("消息处理任务启动: {}", Thread.currentThread().getName());
            
            while (running) {
                try {
                    // 从队列中获取消息
                    SocketMessage message = messageQueue.poll(1, TimeUnit.SECONDS);
                    
                    if (message != null) {
                        processMessage(message);
                    }
                } catch (InterruptedException e) {
                    logger.info("消息处理任务被中断: {}", Thread.currentThread().getName());
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("消息处理任务发生异常: {}", e.getMessage(), e);
                }
            }
            
            logger.info("消息处理任务结束: {}", Thread.currentThread().getName());
        }
        
        /**
         * 处理单个消息
         * 
         * @param message 要处理的消息
         */
        private void processMessage(SocketMessage message) {
            try {
                logger.debug("开始处理消息: messageId={}, sessionId={}, type={}", 
                    message.getMessageId(), message.getSessionId(), message.getType());
                
                // 根据消息类型进行不同的处理
                switch (message.getType()) {
                    case TEXT:
                    case BINARY:
                        handleBusinessMessage(message);
                        break;
                    case SYSTEM:
                        handleSystemMessage(message);
                        break;
                    default:
                        logger.debug("消息类型无需特殊处理: type={}", message.getType());
                        break;
                }
                
                // 更新消息状态
                message.setStatus(SocketMessage.MessageStatus.RECEIVED);
                
                logger.debug("消息处理完成: messageId={}, sessionId={}", 
                    message.getMessageId(), message.getSessionId());
                
            } catch (Exception e) {
                logger.error("处理消息时发生异常: messageId={}, sessionId={}, error={}", 
                    message.getMessageId(), message.getSessionId(), e.getMessage(), e);
                
                // 更新消息状态为失败
                message.setStatus(SocketMessage.MessageStatus.FAILED);
            }
        }
        
        /**
         * 处理业务消息
         * 
         * @param message 业务消息
         */
        private void handleBusinessMessage(SocketMessage message) {
            // 委托给消息处理服务进行具体的业务逻辑处理
            messageProcessingService.processBusinessMessage(message);
            
            // 如果消息指定了接收者，进行转发
            if (message.getReceiverId() != null) {
                forwardMessage(message);
            }
        }
        
        /**
         * 处理系统消息
         * 
         * @param message 系统消息
         */
        private void handleSystemMessage(SocketMessage message) {
            // 处理系统级消息，如通知、公告等
            messageProcessingService.processSystemMessage(message);
        }
        
        /**
         * 转发消息
         * 
         * @param message 要转发的消息
         */
        private void forwardMessage(SocketMessage message) {
            String receiverId = message.getReceiverId();
            
            // 尝试作为会话ID发送
            if (sessionManager.getSession(receiverId) != null) {
                sessionManager.sendMessageToSession(receiverId, message);
                logger.debug("消息转发到会话: messageId={}, targetSessionId={}", 
                    message.getMessageId(), receiverId);
            } else {
                // 尝试作为用户ID发送
                int sentCount = sessionManager.sendMessageToUser(receiverId, message);
                logger.debug("消息转发到用户: messageId={}, targetUserId={}, sentCount={}", 
                    message.getMessageId(), receiverId, sentCount);
            }
        }
    }
    
    /**
     * 消息队列统计信息
     */
    public static class MessageQueueStats {
        private final int queueSize;
        private final int remainingCapacity;
        private final int activeThreads;
        private final long completedTasks;
        private final long totalTasks;
        
        public MessageQueueStats(int queueSize, int remainingCapacity, int activeThreads, 
                                long completedTasks, long totalTasks) {
            this.queueSize = queueSize;
            this.remainingCapacity = remainingCapacity;
            this.activeThreads = activeThreads;
            this.completedTasks = completedTasks;
            this.totalTasks = totalTasks;
        }
        
        // Getter 方法
        public int getQueueSize() { return queueSize; }
        public int getRemainingCapacity() { return remainingCapacity; }
        public int getActiveThreads() { return activeThreads; }
        public long getCompletedTasks() { return completedTasks; }
        public long getTotalTasks() { return totalTasks; }
        
        @Override
        public String toString() {
            return String.format("MessageQueueStats{queueSize=%d, remainingCapacity=%d, activeThreads=%d, completedTasks=%d, totalTasks=%d}", 
                queueSize, remainingCapacity, activeThreads, completedTasks, totalTasks);
        }
    }
}
