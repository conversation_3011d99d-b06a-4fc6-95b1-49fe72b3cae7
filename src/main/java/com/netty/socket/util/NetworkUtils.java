package com.netty.socket.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 * 网络工具类
 * 提供网络相关的便捷方法
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
public class NetworkUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(NetworkUtils.class);
    
    /**
     * 获取本机IP地址
     * 
     * @return 本机IP地址
     */
    public static String getLocalIpAddress() {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                
                // 跳过回环接口和未启用的接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }
                
                Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    InetAddress inetAddress = inetAddresses.nextElement();
                    
                    // 跳过回环地址和IPv6地址
                    if (inetAddress.isLoopbackAddress() || !inetAddress.isSiteLocalAddress()) {
                        continue;
                    }
                    
                    return inetAddress.getHostAddress();
                }
            }
        } catch (SocketException e) {
            logger.error("获取本机IP地址失败: {}", e.getMessage(), e);
        }
        
        return "127.0.0.1";
    }
    
    /**
     * 检查端口是否可用
     * 
     * @param port 端口号
     * @return 是否可用
     */
    public static boolean isPortAvailable(int port) {
        try (java.net.ServerSocket serverSocket = new java.net.ServerSocket(port)) {
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 解析客户端地址信息
     * 
     * @param remoteAddress 远程地址字符串
     * @return 地址信息数组 [IP, Port]
     */
    public static String[] parseRemoteAddress(String remoteAddress) {
        if (remoteAddress == null || remoteAddress.trim().isEmpty()) {
            return new String[]{"unknown", "0"};
        }
        
        // 移除前缀斜杠
        if (remoteAddress.startsWith("/")) {
            remoteAddress = remoteAddress.substring(1);
        }
        
        // 分割IP和端口
        String[] parts = remoteAddress.split(":");
        if (parts.length == 2) {
            return new String[]{parts[0], parts[1]};
        }
        
        return new String[]{"unknown", "0"};
    }
}
