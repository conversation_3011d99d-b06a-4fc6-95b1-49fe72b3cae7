package com.netty.socket;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * Netty Socket服务器主启动类
 * 基于Spring Boot 3.4和Netty的高性能Socket服务框架
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@SpringBootApplication
public class NettySocketServerApplication {
    
    private static final Logger logger = LoggerFactory.getLogger(NettySocketServerApplication.class);
    
    public static void main(String[] args) {
        try {
            logger.info("正在启动Netty Socket服务器应用...");
            
            // 设置系统属性
            System.setProperty("java.net.preferIPv4Stack", "true");
            System.setProperty("io.netty.leakDetection.level", "SIMPLE");
            
            // 启动Spring Boot应用
            ConfigurableApplicationContext context = SpringApplication.run(NettySocketServerApplication.class, args);
            
            // 获取应用信息
            String applicationName = context.getEnvironment().getProperty("spring.application.name", "Netty Socket Server");
            String serverPort = context.getEnvironment().getProperty("server.port", "8080");
            String nettyPort = context.getEnvironment().getProperty("netty.socket.primary-port", "9999");
            String profiles = String.join(",", context.getEnvironment().getActiveProfiles());
            
            logger.info("========================================");
            logger.info("应用启动成功！");
            logger.info("应用名称: {}", applicationName);
            logger.info("Spring Boot Web端口: {}", serverPort);
            logger.info("Netty Socket端口: {}", nettyPort);
            logger.info("激活的配置文件: {}", profiles.isEmpty() ? "default" : profiles);
            logger.info("管理接口: http://localhost:{}/api/netty/status", serverPort);
            logger.info("健康检查: http://localhost:{}/actuator/health", serverPort);
            logger.info("========================================");
            
            // 添加优雅关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                logger.info("正在优雅关闭应用...");
                context.close();
                logger.info("应用已关闭");
            }));
            
        } catch (Exception e) {
            logger.error("应用启动失败: {}", e.getMessage(), e);
            System.exit(1);
        }
    }
}
