package com.netty.socket.handler;

import com.netty.socket.manager.SessionManager;
import com.netty.socket.model.SessionInfo;
import com.netty.socket.model.SocketMessage;
import com.netty.socket.service.MessageRoutingService;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * Socket服务器处理器
 * 处理客户端连接、消息接收、心跳检测等核心逻辑
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@Component
public class SocketServerHandler extends SimpleChannelInboundHandler<SocketMessage> {
    
    private static final Logger logger = LoggerFactory.getLogger(SocketServerHandler.class);
    
    @Autowired
    private SessionManager sessionManager;
    
    @Autowired
    private MessageRoutingService messageRoutingService;
    
    /**
     * 客户端连接建立时调用
     */
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        try {
            // 生成唯一的会话ID
            String sessionId = generateSessionId();
            
            // 创建会话信息
            SessionInfo sessionInfo = new SessionInfo(sessionId, ctx.channel());
            sessionInfo.setStatus(SessionInfo.SessionStatus.CONNECTED);
            
            // 将会话ID绑定到Channel
            ctx.channel().attr(SessionManager.SESSION_ID_KEY).set(sessionId);
            
            // 注册会话
            sessionManager.addSession(sessionInfo);
            
            logger.info("客户端连接建立: sessionId={}, remoteAddress={}, localPort={}", 
                sessionId, ctx.channel().remoteAddress(), sessionInfo.getServerPort());
            
            // 发送连接确认消息
            SocketMessage connectMessage = new SocketMessage(sessionId, SocketMessage.MessageType.CONNECT, "连接成功");
            connectMessage.setMessageId(UUID.randomUUID().toString());
            ctx.writeAndFlush(connectMessage);
            
            super.channelActive(ctx);
            
        } catch (Exception e) {
            logger.error("处理客户端连接时发生异常: {}", e.getMessage(), e);
            ctx.close();
        }
    }
    
    /**
     * 客户端连接断开时调用
     */
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        try {
            String sessionId = ctx.channel().attr(SessionManager.SESSION_ID_KEY).get();
            
            if (sessionId != null) {
                // 移除会话
                SessionInfo sessionInfo = sessionManager.removeSession(sessionId);
                
                if (sessionInfo != null) {
                    sessionInfo.setStatus(SessionInfo.SessionStatus.DISCONNECTED);
                    logger.info("客户端连接断开: sessionId={}, remoteAddress={}, 连接时长={}分钟", 
                        sessionId, ctx.channel().remoteAddress(), 
                        java.time.Duration.between(sessionInfo.getCreateTime(), java.time.LocalDateTime.now()).toMinutes());
                } else {
                    logger.warn("会话信息不存在: sessionId={}", sessionId);
                }
            }
            
            super.channelInactive(ctx);
            
        } catch (Exception e) {
            logger.error("处理客户端断开连接时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 接收到客户端消息时调用
     */
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, SocketMessage msg) throws Exception {
        try {
            String sessionId = ctx.channel().attr(SessionManager.SESSION_ID_KEY).get();
            
            if (sessionId == null) {
                logger.warn("接收到消息但会话ID为空，关闭连接: {}", ctx.channel().remoteAddress());
                ctx.close();
                return;
            }
            
            // 更新会话信息
            SessionInfo sessionInfo = sessionManager.getSession(sessionId);
            if (sessionInfo != null) {
                sessionInfo.updateLastActiveTime();
                sessionInfo.incrementReceivedMessageCount();
                sessionInfo.resetHeartbeatFailureCount();
            }
            
            // 设置消息的会话ID（如果消息中没有）
            if (msg.getSessionId() == null) {
                msg.setSessionId(sessionId);
            }
            
            // 设置消息ID（如果消息中没有）
            if (msg.getMessageId() == null) {
                msg.setMessageId(UUID.randomUUID().toString());
            }
            
            logger.debug("接收到消息: sessionId={}, messageId={}, type={}, content={}", 
                sessionId, msg.getMessageId(), msg.getType(), msg.getContent());
            
            // 处理不同类型的消息
            switch (msg.getType()) {
                case HEARTBEAT:
                    handleHeartbeat(ctx, msg, sessionInfo);
                    break;
                case AUTH:
                    handleAuthentication(ctx, msg, sessionInfo);
                    break;
                case TEXT:
                case BINARY:
                    handleBusinessMessage(ctx, msg, sessionInfo);
                    break;
                case DISCONNECT:
                    handleDisconnect(ctx, msg, sessionInfo);
                    break;
                default:
                    logger.warn("未知的消息类型: {}, sessionId={}", msg.getType(), sessionId);
                    break;
            }
            
        } catch (Exception e) {
            logger.error("处理消息时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理心跳消息
     */
    private void handleHeartbeat(ChannelHandlerContext ctx, SocketMessage msg, SessionInfo sessionInfo) {
        logger.debug("收到心跳消息: sessionId={}", msg.getSessionId());
        
        // 发送心跳响应
        SocketMessage heartbeatResponse = new SocketMessage(msg.getSessionId(), SocketMessage.MessageType.HEARTBEAT, "pong");
        heartbeatResponse.setMessageId(UUID.randomUUID().toString());
        ctx.writeAndFlush(heartbeatResponse);
        
        if (sessionInfo != null) {
            sessionInfo.resetHeartbeatFailureCount();
        }
    }
    
    /**
     * 处理认证消息
     */
    private void handleAuthentication(ChannelHandlerContext ctx, SocketMessage msg, SessionInfo sessionInfo) {
        logger.info("收到认证消息: sessionId={}, content={}", msg.getSessionId(), msg.getContent());
        
        // 这里可以实现具体的认证逻辑
        // 示例：简单的token验证
        boolean authSuccess = validateAuthToken(msg.getContent());
        
        SocketMessage authResponse;
        if (authSuccess) {
            authResponse = new SocketMessage(msg.getSessionId(), SocketMessage.MessageType.ACK, "认证成功");
            if (sessionInfo != null) {
                sessionInfo.setStatus(SessionInfo.SessionStatus.AUTHENTICATED);
                sessionInfo.setUserId(extractUserIdFromToken(msg.getContent()));
            }
            logger.info("用户认证成功: sessionId={}", msg.getSessionId());
        } else {
            authResponse = new SocketMessage(msg.getSessionId(), SocketMessage.MessageType.ERROR, "认证失败");
            logger.warn("用户认证失败: sessionId={}", msg.getSessionId());
        }
        
        authResponse.setMessageId(UUID.randomUUID().toString());
        ctx.writeAndFlush(authResponse);
    }
    
    /**
     * 处理业务消息
     */
    private void handleBusinessMessage(ChannelHandlerContext ctx, SocketMessage msg, SessionInfo sessionInfo) {
        logger.debug("收到业务消息: sessionId={}, type={}, content={}", 
            msg.getSessionId(), msg.getType(), msg.getContent());
        
        // 将消息路由到消息处理服务
        messageRoutingService.routeMessage(msg);
        
        // 发送确认消息
        SocketMessage ackMessage = new SocketMessage(msg.getSessionId(), SocketMessage.MessageType.ACK, "消息已接收");
        ackMessage.setMessageId(UUID.randomUUID().toString());
        ctx.writeAndFlush(ackMessage);
    }
    
    /**
     * 处理断开连接消息
     */
    private void handleDisconnect(ChannelHandlerContext ctx, SocketMessage msg, SessionInfo sessionInfo) {
        logger.info("收到断开连接消息: sessionId={}", msg.getSessionId());
        
        // 发送断开确认消息
        SocketMessage disconnectResponse = new SocketMessage(msg.getSessionId(), SocketMessage.MessageType.ACK, "断开连接确认");
        disconnectResponse.setMessageId(UUID.randomUUID().toString());
        ctx.writeAndFlush(disconnectResponse).addListener(future -> {
            // 发送完确认消息后关闭连接
            ctx.close();
        });
    }
    
    /**
     * 处理空闲状态事件（心跳检测）
     */
    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent event = (IdleStateEvent) evt;
            String sessionId = ctx.channel().attr(SessionManager.SESSION_ID_KEY).get();
            
            if (event.state() == IdleState.READER_IDLE) {
                // 读空闲，表示客户端可能已经断开连接
                logger.warn("客户端读空闲超时: sessionId={}, 关闭连接", sessionId);
                
                SessionInfo sessionInfo = sessionManager.getSession(sessionId);
                if (sessionInfo != null) {
                    sessionInfo.incrementHeartbeatFailureCount();
                }
                
                ctx.close();
            }
        } else {
            super.userEventTriggered(ctx, evt);
        }
    }
    
    /**
     * 异常处理
     */
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        String sessionId = ctx.channel().attr(SessionManager.SESSION_ID_KEY).get();
        logger.error("连接异常: sessionId={}, remoteAddress={}, error={}", 
            sessionId, ctx.channel().remoteAddress(), cause.getMessage(), cause);
        ctx.close();
    }
    
    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return "session_" + UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 验证认证令牌（示例实现）
     */
    private boolean validateAuthToken(String token) {
        // 这里实现具体的token验证逻辑
        // 示例：简单的非空检查
        return token != null && !token.trim().isEmpty();
    }
    
    /**
     * 从令牌中提取用户ID（示例实现）
     */
    private String extractUserIdFromToken(String token) {
        // 这里实现从token中提取用户ID的逻辑
        // 示例：使用token作为用户ID
        return "user_" + token.hashCode();
    }
}
