package com.netty.socket.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.netty.socket.model.SocketMessage;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

/**
 * 消息编码器
 * 将SocketMessage对象编码为字节流发送给客户端
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
public class MessageEncoder extends MessageToByteEncoder<SocketMessage> {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageEncoder.class);
    
    /**
     * JSON对象映射器
     */
    private final ObjectMapper objectMapper;
    
    public MessageEncoder() {
        this.objectMapper = new ObjectMapper();
        // 配置ObjectMapper以处理时间格式
        this.objectMapper.findAndRegisterModules();
    }
    
    @Override
    protected void encode(ChannelHandlerContext ctx, SocketMessage msg, ByteBuf out) throws Exception {
        try {
            // 将SocketMessage对象转换为JSON字符串
            String messageJson = objectMapper.writeValueAsString(msg);
            
            // 将JSON字符串转换为字节数组
            byte[] messageBytes = messageJson.getBytes(StandardCharsets.UTF_8);
            
            // 写入消息长度（4字节）
            out.writeInt(messageBytes.length);
            
            // 写入消息体
            out.writeBytes(messageBytes);
            
            logger.debug("成功编码消息: sessionId={}, type={}, messageLength={}", 
                msg.getSessionId(), msg.getType(), messageBytes.length);
                
        } catch (Exception e) {
            logger.error("消息编码失败: {}, 消息: {}", e.getMessage(), msg, e);
            throw e;
        }
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.error("MessageEncoder异常: {}, 连接: {}", cause.getMessage(), ctx.channel().remoteAddress(), cause);
        ctx.close();
    }
}
