package com.netty.socket.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.netty.socket.model.SocketMessage;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 消息解码器
 * 将接收到的字节流解码为SocketMessage对象
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
public class MessageDecoder extends ByteToMessageDecoder {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageDecoder.class);
    
    /**
     * JSON对象映射器
     */
    private final ObjectMapper objectMapper;
    
    /**
     * 消息头长度（4字节，用于存储消息体长度）
     */
    private static final int HEADER_LENGTH = 4;
    
    /**
     * 最大消息长度（1MB）
     */
    private static final int MAX_MESSAGE_LENGTH = 1024 * 1024;
    
    public MessageDecoder() {
        this.objectMapper = new ObjectMapper();
        // 配置ObjectMapper以处理时间格式
        this.objectMapper.findAndRegisterModules();
    }
    
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        try {
            // 检查是否有足够的字节来读取消息头
            if (in.readableBytes() < HEADER_LENGTH) {
                return;
            }
            
            // 标记当前读取位置
            in.markReaderIndex();
            
            // 读取消息长度
            int messageLength = in.readInt();
            
            // 验证消息长度
            if (messageLength <= 0 || messageLength > MAX_MESSAGE_LENGTH) {
                logger.error("无效的消息长度: {}, 关闭连接: {}", messageLength, ctx.channel().remoteAddress());
                ctx.close();
                return;
            }
            
            // 检查是否有足够的字节来读取完整消息
            if (in.readableBytes() < messageLength) {
                // 重置读取位置，等待更多数据
                in.resetReaderIndex();
                return;
            }
            
            // 读取消息体
            byte[] messageBytes = new byte[messageLength];
            in.readBytes(messageBytes);
            
            // 将字节数组转换为字符串
            String messageJson = new String(messageBytes, StandardCharsets.UTF_8);
            
            // 解析JSON为SocketMessage对象
            SocketMessage message = parseMessage(messageJson);
            
            if (message != null) {
                logger.debug("成功解码消息: sessionId={}, type={}, content={}", 
                    message.getSessionId(), message.getType(), message.getContent());
                out.add(message);
            } else {
                logger.warn("解码消息失败，无法解析JSON: {}", messageJson);
            }
            
        } catch (Exception e) {
            logger.error("消息解码过程中发生异常: {}", e.getMessage(), e);
            // 发生异常时关闭连接
            ctx.close();
        }
    }
    
    /**
     * 解析JSON字符串为SocketMessage对象
     * 
     * @param messageJson JSON字符串
     * @return SocketMessage对象，解析失败时返回null
     */
    private SocketMessage parseMessage(String messageJson) {
        try {
            return objectMapper.readValue(messageJson, SocketMessage.class);
        } catch (Exception e) {
            logger.error("JSON解析失败: {}, 原始消息: {}", e.getMessage(), messageJson);
            return null;
        }
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.error("MessageDecoder异常: {}, 连接: {}", cause.getMessage(), ctx.channel().remoteAddress(), cause);
        ctx.close();
    }
}
