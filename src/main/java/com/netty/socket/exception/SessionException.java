package com.netty.socket.exception;

/**
 * 会话相关异常
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
public class SessionException extends NettySocketException {
    
    public SessionException(String message) {
        super("SESSION_ERROR", message);
    }
    
    public SessionException(String message, Throwable cause) {
        super("SESSION_ERROR", message, cause);
    }
    
    public SessionException(String errorCode, String message) {
        super(errorCode, message);
    }
    
    public SessionException(String errorCode, String message, Object errorDetails) {
        super(errorCode, message, errorDetails);
    }
}
