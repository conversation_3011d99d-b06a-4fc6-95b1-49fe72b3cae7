package com.netty.socket.exception;

/**
 * 消息路由相关异常
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
public class MessageRoutingException extends NettySocketException {
    
    public MessageRoutingException(String message) {
        super("MESSAGE_ROUTING_ERROR", message);
    }
    
    public MessageRoutingException(String message, Throwable cause) {
        super("MESSAGE_ROUTING_ERROR", message, cause);
    }
    
    public MessageRoutingException(String errorCode, String message) {
        super(errorCode, message);
    }
    
    public MessageRoutingException(String errorCode, String message, Object errorDetails) {
        super(errorCode, message, errorDetails);
    }
}
