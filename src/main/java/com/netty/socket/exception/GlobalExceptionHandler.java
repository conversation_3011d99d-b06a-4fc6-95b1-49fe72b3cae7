package com.netty.socket.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 统一处理应用中的异常，返回标准化的错误响应
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    /**
     * 处理Netty Socket自定义异常
     * 
     * @param ex 异常对象
     * @param request Web请求
     * @return 错误响应
     */
    @ExceptionHandler(NettySocketException.class)
    public ResponseEntity<Map<String, Object>> handleNettySocketException(
            NettySocketException ex, WebRequest request) {
        
        logger.error("Netty Socket异常: errorCode={}, message={}", ex.getErrorCode(), ex.getMessage(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            ex.getErrorCode() != null ? ex.getErrorCode() : "NETTY_SOCKET_ERROR",
            ex.getMessage(),
            ex.getErrorDetails(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
    
    /**
     * 处理会话异常
     * 
     * @param ex 异常对象
     * @param request Web请求
     * @return 错误响应
     */
    @ExceptionHandler(SessionException.class)
    public ResponseEntity<Map<String, Object>> handleSessionException(
            SessionException ex, WebRequest request) {
        
        logger.error("会话异常: errorCode={}, message={}", ex.getErrorCode(), ex.getMessage(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            ex.getErrorCode(),
            ex.getMessage(),
            ex.getErrorDetails(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }
    
    /**
     * 处理消息路由异常
     * 
     * @param ex 异常对象
     * @param request Web请求
     * @return 错误响应
     */
    @ExceptionHandler(MessageRoutingException.class)
    public ResponseEntity<Map<String, Object>> handleMessageRoutingException(
            MessageRoutingException ex, WebRequest request) {
        
        logger.error("消息路由异常: errorCode={}, message={}", ex.getErrorCode(), ex.getMessage(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            ex.getErrorCode(),
            ex.getMessage(),
            ex.getErrorDetails(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
    
    /**
     * 处理非法参数异常
     * 
     * @param ex 异常对象
     * @param request Web请求
     * @return 错误响应
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Map<String, Object>> handleIllegalArgumentException(
            IllegalArgumentException ex, WebRequest request) {
        
        logger.error("非法参数异常: message={}", ex.getMessage(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "ILLEGAL_ARGUMENT",
            ex.getMessage(),
            null,
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }
    
    /**
     * 处理空指针异常
     * 
     * @param ex 异常对象
     * @param request Web请求
     * @return 错误响应
     */
    @ExceptionHandler(NullPointerException.class)
    public ResponseEntity<Map<String, Object>> handleNullPointerException(
            NullPointerException ex, WebRequest request) {
        
        logger.error("空指针异常: message={}", ex.getMessage(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "NULL_POINTER",
            "系统内部错误，请联系管理员",
            null,
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
    
    /**
     * 处理运行时异常
     * 
     * @param ex 异常对象
     * @param request Web请求
     * @return 错误响应
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<Map<String, Object>> handleRuntimeException(
            RuntimeException ex, WebRequest request) {
        
        logger.error("运行时异常: message={}", ex.getMessage(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "RUNTIME_ERROR",
            "系统运行时错误: " + ex.getMessage(),
            null,
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
    
    /**
     * 处理通用异常
     * 
     * @param ex 异常对象
     * @param request Web请求
     * @return 错误响应
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(
            Exception ex, WebRequest request) {
        
        logger.error("未知异常: message={}", ex.getMessage(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "UNKNOWN_ERROR",
            "系统发生未知错误，请联系管理员",
            null,
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
    
    /**
     * 创建标准化的错误响应
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param details 错误详情
     * @param path 请求路径
     * @return 错误响应Map
     */
    private Map<String, Object> createErrorResponse(String errorCode, String message, 
                                                   Object details, String path) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", errorCode);
        errorResponse.put("message", message);
        errorResponse.put("timestamp", LocalDateTime.now());
        errorResponse.put("path", path);
        
        if (details != null) {
            errorResponse.put("details", details);
        }
        
        return errorResponse;
    }
}
