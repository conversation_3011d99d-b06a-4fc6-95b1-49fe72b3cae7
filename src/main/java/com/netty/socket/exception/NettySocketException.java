package com.netty.socket.exception;

/**
 * Netty Socket服务器自定义异常基类
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
public class NettySocketException extends RuntimeException {
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 错误详细信息
     */
    private Object errorDetails;
    
    public NettySocketException(String message) {
        super(message);
    }
    
    public NettySocketException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public NettySocketException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public NettySocketException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public NettySocketException(String errorCode, String message, Object errorDetails) {
        super(message);
        this.errorCode = errorCode;
        this.errorDetails = errorDetails;
    }
    
    public NettySocketException(String errorCode, String message, Object errorDetails, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorDetails = errorDetails;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    public Object getErrorDetails() {
        return errorDetails;
    }
    
    public void setErrorDetails(Object errorDetails) {
        this.errorDetails = errorDetails;
    }
}
