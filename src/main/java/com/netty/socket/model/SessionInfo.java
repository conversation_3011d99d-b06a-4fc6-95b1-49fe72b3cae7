package com.netty.socket.model;

import io.netty.channel.Channel;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 会话信息实体类
 * 用于管理客户端连接会话的详细信息
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
public class SessionInfo {
    
    /**
     * 会话唯一标识
     */
    private String sessionId;
    
    /**
     * 用户ID（可选，用于用户认证后绑定）
     */
    private String userId;
    
    /**
     * Netty Channel 对象
     */
    private Channel channel;
    
    /**
     * 客户端IP地址
     */
    private String clientIp;
    
    /**
     * 客户端端口
     */
    private Integer clientPort;
    
    /**
     * 服务器端口（用于多端口场景）
     */
    private Integer serverPort;
    
    /**
     * 会话创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;
    
    /**
     * 会话状态
     */
    private SessionStatus status;
    
    /**
     * 会话属性（用于存储自定义数据）
     */
    private Map<String, Object> attributes;
    
    /**
     * 发送消息计数
     */
    private AtomicLong sentMessageCount;
    
    /**
     * 接收消息计数
     */
    private AtomicLong receivedMessageCount;
    
    /**
     * 心跳失败次数
     */
    private AtomicLong heartbeatFailureCount;
    
    /**
     * 客户端信息（浏览器、设备等）
     */
    private String userAgent;
    
    /**
     * 会话标签（用于分组管理）
     */
    private String tag;
    
    /**
     * 会话状态枚举
     */
    public enum SessionStatus {
        /**
         * 连接中
         */
        CONNECTING,
        
        /**
         * 已连接
         */
        CONNECTED,
        
        /**
         * 已认证
         */
        AUTHENTICATED,
        
        /**
         * 空闲状态
         */
        IDLE,
        
        /**
         * 断开连接中
         */
        DISCONNECTING,
        
        /**
         * 已断开
         */
        DISCONNECTED,
        
        /**
         * 异常状态
         */
        ERROR
    }
    
    // 构造函数
    public SessionInfo() {
        this.createTime = LocalDateTime.now();
        this.lastActiveTime = LocalDateTime.now();
        this.status = SessionStatus.CONNECTING;
        this.attributes = new ConcurrentHashMap<>();
        this.sentMessageCount = new AtomicLong(0);
        this.receivedMessageCount = new AtomicLong(0);
        this.heartbeatFailureCount = new AtomicLong(0);
    }
    
    public SessionInfo(String sessionId, Channel channel) {
        this();
        this.sessionId = sessionId;
        this.channel = channel;
        // 解析客户端地址信息
        if (channel != null && channel.remoteAddress() != null) {
            String remoteAddress = channel.remoteAddress().toString();
            if (remoteAddress.startsWith("/")) {
                remoteAddress = remoteAddress.substring(1);
            }
            String[] parts = remoteAddress.split(":");
            if (parts.length == 2) {
                this.clientIp = parts[0];
                try {
                    this.clientPort = Integer.parseInt(parts[1]);
                } catch (NumberFormatException e) {
                    this.clientPort = 0;
                }
            }
        }
        // 解析服务器端口信息
        if (channel != null && channel.localAddress() != null) {
            String localAddress = channel.localAddress().toString();
            String[] parts = localAddress.split(":");
            if (parts.length == 2) {
                try {
                    this.serverPort = Integer.parseInt(parts[1]);
                } catch (NumberFormatException e) {
                    this.serverPort = 0;
                }
            }
        }
    }
    
    /**
     * 更新最后活跃时间
     */
    public void updateLastActiveTime() {
        this.lastActiveTime = LocalDateTime.now();
    }
    
    /**
     * 增加发送消息计数
     */
    public void incrementSentMessageCount() {
        this.sentMessageCount.incrementAndGet();
    }
    
    /**
     * 增加接收消息计数
     */
    public void incrementReceivedMessageCount() {
        this.receivedMessageCount.incrementAndGet();
    }
    
    /**
     * 增加心跳失败次数
     */
    public void incrementHeartbeatFailureCount() {
        this.heartbeatFailureCount.incrementAndGet();
    }
    
    /**
     * 重置心跳失败次数
     */
    public void resetHeartbeatFailureCount() {
        this.heartbeatFailureCount.set(0);
    }
    
    /**
     * 设置会话属性
     */
    public void setAttribute(String key, Object value) {
        this.attributes.put(key, value);
    }
    
    /**
     * 获取会话属性
     */
    public Object getAttribute(String key) {
        return this.attributes.get(key);
    }
    
    /**
     * 移除会话属性
     */
    public Object removeAttribute(String key) {
        return this.attributes.remove(key);
    }
    
    /**
     * 检查Channel是否活跃
     */
    public boolean isActive() {
        return channel != null && channel.isActive();
    }
    
    // Getter 和 Setter 方法
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public Channel getChannel() {
        return channel;
    }
    
    public void setChannel(Channel channel) {
        this.channel = channel;
    }
    
    public String getClientIp() {
        return clientIp;
    }
    
    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }
    
    public Integer getClientPort() {
        return clientPort;
    }
    
    public void setClientPort(Integer clientPort) {
        this.clientPort = clientPort;
    }
    
    public Integer getServerPort() {
        return serverPort;
    }
    
    public void setServerPort(Integer serverPort) {
        this.serverPort = serverPort;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getLastActiveTime() {
        return lastActiveTime;
    }
    
    public void setLastActiveTime(LocalDateTime lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }
    
    public SessionStatus getStatus() {
        return status;
    }
    
    public void setStatus(SessionStatus status) {
        this.status = status;
    }
    
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }
    
    public long getSentMessageCount() {
        return sentMessageCount.get();
    }
    
    public long getReceivedMessageCount() {
        return receivedMessageCount.get();
    }
    
    public long getHeartbeatFailureCount() {
        return heartbeatFailureCount.get();
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public String getTag() {
        return tag;
    }
    
    public void setTag(String tag) {
        this.tag = tag;
    }
    
    @Override
    public String toString() {
        return "SessionInfo{" +
                "sessionId='" + sessionId + '\'' +
                ", userId='" + userId + '\'' +
                ", clientIp='" + clientIp + '\'' +
                ", clientPort=" + clientPort +
                ", serverPort=" + serverPort +
                ", createTime=" + createTime +
                ", lastActiveTime=" + lastActiveTime +
                ", status=" + status +
                ", sentMessageCount=" + sentMessageCount.get() +
                ", receivedMessageCount=" + receivedMessageCount.get() +
                ", heartbeatFailureCount=" + heartbeatFailureCount.get() +
                ", tag='" + tag + '\'' +
                '}';
    }
}
