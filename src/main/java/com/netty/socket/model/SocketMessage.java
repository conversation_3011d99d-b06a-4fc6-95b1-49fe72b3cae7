package com.netty.socket.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Socket消息实体类
 * 用于封装客户端和服务端之间传输的消息
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SocketMessage {
    
    /**
     * 消息唯一标识
     */
    private String messageId;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 消息类型
     */
    private MessageType type;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息数据（可选的额外数据）
     */
    private Map<String, Object> data;
    
    /**
     * 发送者ID
     */
    private String senderId;
    
    /**
     * 接收者ID（可选，用于点对点消息）
     */
    private String receiverId;
    
    /**
     * 消息创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    /**
     * 消息状态
     */
    private MessageStatus status;
    
    /**
     * 消息类型枚举
     */
    public enum MessageType {
        /**
         * 连接建立
         */
        CONNECT,
        
        /**
         * 断开连接
         */
        DISCONNECT,
        
        /**
         * 心跳消息
         */
        HEARTBEAT,
        
        /**
         * 普通文本消息
         */
        TEXT,
        
        /**
         * 二进制消息
         */
        BINARY,
        
        /**
         * 系统通知
         */
        SYSTEM,
        
        /**
         * 错误消息
         */
        ERROR,
        
        /**
         * 认证消息
         */
        AUTH,
        
        /**
         * 确认消息
         */
        ACK
    }
    
    /**
     * 消息状态枚举
     */
    public enum MessageStatus {
        /**
         * 待发送
         */
        PENDING,
        
        /**
         * 已发送
         */
        SENT,
        
        /**
         * 已接收
         */
        RECEIVED,
        
        /**
         * 发送失败
         */
        FAILED,
        
        /**
         * 已确认
         */
        ACKNOWLEDGED
    }
    
    // 构造函数
    public SocketMessage() {
        this.timestamp = LocalDateTime.now();
        this.status = MessageStatus.PENDING;
    }
    
    public SocketMessage(MessageType type, String content) {
        this();
        this.type = type;
        this.content = content;
    }
    
    public SocketMessage(String sessionId, MessageType type, String content) {
        this(type, content);
        this.sessionId = sessionId;
    }
    
    // Getter 和 Setter 方法
    public String getMessageId() {
        return messageId;
    }
    
    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public MessageType getType() {
        return type;
    }
    
    public void setType(MessageType type) {
        this.type = type;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public Map<String, Object> getData() {
        return data;
    }
    
    public void setData(Map<String, Object> data) {
        this.data = data;
    }
    
    public String getSenderId() {
        return senderId;
    }
    
    public void setSenderId(String senderId) {
        this.senderId = senderId;
    }
    
    public String getReceiverId() {
        return receiverId;
    }
    
    public void setReceiverId(String receiverId) {
        this.receiverId = receiverId;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public MessageStatus getStatus() {
        return status;
    }
    
    public void setStatus(MessageStatus status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "SocketMessage{" +
                "messageId='" + messageId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", type=" + type +
                ", content='" + content + '\'' +
                ", senderId='" + senderId + '\'' +
                ", receiverId='" + receiverId + '\'' +
                ", timestamp=" + timestamp +
                ", status=" + status +
                '}';
    }
}
