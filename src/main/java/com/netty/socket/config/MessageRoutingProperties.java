package com.netty.socket.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 消息路由配置属性类
 * 用于从application.yml中读取消息路由相关配置
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "message.routing")
public class MessageRoutingProperties {
    
    /**
     * 消息队列大小
     */
    private int queueSize = 1000;
    
    /**
     * 消息处理线程池大小
     */
    private int threadPoolSize = 16;
    
    /**
     * 消息超时时间（秒）
     */
    private int timeout = 30;
    
    // Getter 和 Setter 方法
    public int getQueueSize() {
        return queueSize;
    }
    
    public void setQueueSize(int queueSize) {
        this.queueSize = queueSize;
    }
    
    public int getThreadPoolSize() {
        return threadPoolSize;
    }
    
    public void setThreadPoolSize(int threadPoolSize) {
        this.threadPoolSize = threadPoolSize;
    }
    
    public int getTimeout() {
        return timeout;
    }
    
    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
}
