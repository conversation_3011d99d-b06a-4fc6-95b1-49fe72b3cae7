package com.netty.socket.config;

import com.netty.socket.service.NettyServerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * Netty服务器配置类
 * 负责配置Netty服务器相关的Bean和启动逻辑
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@Configuration
@EnableConfigurationProperties({
    NettyProperties.class,
    ConnectionPoolProperties.class,
    MessageRoutingProperties.class,
    SessionProperties.class
})
@EnableAsync
@EnableScheduling
public class NettyServerConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(NettyServerConfig.class);
    
    @Autowired
    private NettyProperties nettyProperties;
    
    /**
     * 配置异步任务执行器
     * 
     * @return 线程池任务执行器
     */
    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(8);
        executor.setMaxPoolSize(16);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("NettyAsync-");
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        
        logger.info("异步任务执行器配置完成: corePoolSize=8, maxPoolSize=16, queueCapacity=200");
        return executor;
    }
    
    /**
     * Netty服务器启动器
     * 在Spring Boot应用启动完成后自动启动Netty服务器
     * 
     * @param nettyServerService Netty服务器服务
     * @return CommandLineRunner
     */
    @Bean
    public CommandLineRunner nettyServerStarter(NettyServerService nettyServerService) {
        return args -> {
            logger.info("正在启动Netty Socket服务器...");
            try {
                nettyServerService.startServer();
                logger.info("Netty Socket服务器启动成功");
            } catch (Exception e) {
                logger.error("Netty Socket服务器启动失败: {}", e.getMessage(), e);
                // 可以选择是否退出应用
                // System.exit(1);
            }
        };
    }
}
