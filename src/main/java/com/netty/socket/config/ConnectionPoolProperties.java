package com.netty.socket.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 连接池配置属性类
 * 用于从application.yml中读取连接池相关配置
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "connection.pool")
public class ConnectionPoolProperties {
    
    /**
     * 最大连接数
     */
    private int maxConnections = 10000;
    
    /**
     * 连接空闲超时时间（分钟）
     */
    private int idleTimeout = 30;
    
    /**
     * 清理间隔（分钟）
     */
    private int cleanupInterval = 5;
    
    // Getter 和 Setter 方法
    public int getMaxConnections() {
        return maxConnections;
    }
    
    public void setMaxConnections(int maxConnections) {
        this.maxConnections = maxConnections;
    }
    
    public int getIdleTimeout() {
        return idleTimeout;
    }
    
    public void setIdleTimeout(int idleTimeout) {
        this.idleTimeout = idleTimeout;
    }
    
    public int getCleanupInterval() {
        return cleanupInterval;
    }
    
    public void setCleanupInterval(int cleanupInterval) {
        this.cleanupInterval = cleanupInterval;
    }
}
