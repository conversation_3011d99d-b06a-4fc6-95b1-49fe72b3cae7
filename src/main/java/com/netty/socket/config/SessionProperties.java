package com.netty.socket.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 会话管理配置属性类
 * 用于从application.yml中读取会话管理相关配置
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "session")
public class SessionProperties {
    
    /**
     * 会话超时时间（分钟）
     */
    private int timeout = 60;
    
    /**
     * 会话清理间隔（分钟）
     */
    private int cleanupInterval = 10;
    
    /**
     * 最大会话数
     */
    private int maxSessions = 50000;
    
    // Getter 和 Setter 方法
    public int getTimeout() {
        return timeout;
    }
    
    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
    
    public int getCleanupInterval() {
        return cleanupInterval;
    }
    
    public void setCleanupInterval(int cleanupInterval) {
        this.cleanupInterval = cleanupInterval;
    }
    
    public int getMaxSessions() {
        return maxSessions;
    }
    
    public void setMaxSessions(int maxSessions) {
        this.maxSessions = maxSessions;
    }
}
