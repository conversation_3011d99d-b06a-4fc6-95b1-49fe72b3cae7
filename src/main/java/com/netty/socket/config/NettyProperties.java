package com.netty.socket.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Netty配置属性类
 * 用于从application.yml中读取Netty相关配置
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "netty.socket")
public class NettyProperties {
    
    /**
     * 主端口号
     */
    private int primaryPort = 9999;
    
    /**
     * 扩展端口列表（用于负载均衡）
     */
    private List<Integer> additionalPorts;
    
    /**
     * Boss线程组大小
     */
    private int bossThreadCount = 1;
    
    /**
     * Worker线程组大小
     */
    private int workerThreadCount = 8;
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;
    
    /**
     * 保持连接活跃
     */
    private boolean keepAlive = true;
    
    /**
     * TCP_NODELAY设置
     */
    private boolean tcpNoDelay = true;
    
    /**
     * SO_BACKLOG设置
     */
    private int soBacklog = 1024;
    
    /**
     * SO_RCVBUF设置
     */
    private int soRcvBuf = 65536;
    
    /**
     * SO_SNDBUF设置
     */
    private int soSndBuf = 65536;
    
    /**
     * 心跳配置
     */
    private Heartbeat heartbeat = new Heartbeat();
    
    /**
     * 心跳配置内部类
     */
    public static class Heartbeat {
        /**
         * 心跳间隔（秒）
         */
        private int interval = 30;
        
        /**
         * 心跳超时时间（秒）
         */
        private int timeout = 90;
        
        /**
         * 最大失败次数
         */
        private int maxFailures = 3;
        
        // Getter 和 Setter 方法
        public int getInterval() {
            return interval;
        }
        
        public void setInterval(int interval) {
            this.interval = interval;
        }
        
        public int getTimeout() {
            return timeout;
        }
        
        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }
        
        public int getMaxFailures() {
            return maxFailures;
        }
        
        public void setMaxFailures(int maxFailures) {
            this.maxFailures = maxFailures;
        }
    }
    
    // Getter 和 Setter 方法
    public int getPrimaryPort() {
        return primaryPort;
    }
    
    public void setPrimaryPort(int primaryPort) {
        this.primaryPort = primaryPort;
    }
    
    public List<Integer> getAdditionalPorts() {
        return additionalPorts;
    }
    
    public void setAdditionalPorts(List<Integer> additionalPorts) {
        this.additionalPorts = additionalPorts;
    }
    
    public int getBossThreadCount() {
        return bossThreadCount;
    }
    
    public void setBossThreadCount(int bossThreadCount) {
        this.bossThreadCount = bossThreadCount;
    }
    
    public int getWorkerThreadCount() {
        return workerThreadCount;
    }
    
    public void setWorkerThreadCount(int workerThreadCount) {
        this.workerThreadCount = workerThreadCount;
    }
    
    public int getConnectTimeout() {
        return connectTimeout;
    }
    
    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }
    
    public boolean isKeepAlive() {
        return keepAlive;
    }
    
    public void setKeepAlive(boolean keepAlive) {
        this.keepAlive = keepAlive;
    }
    
    public boolean isTcpNoDelay() {
        return tcpNoDelay;
    }
    
    public void setTcpNoDelay(boolean tcpNoDelay) {
        this.tcpNoDelay = tcpNoDelay;
    }
    
    public int getSoBacklog() {
        return soBacklog;
    }
    
    public void setSoBacklog(int soBacklog) {
        this.soBacklog = soBacklog;
    }
    
    public int getSoRcvBuf() {
        return soRcvBuf;
    }
    
    public void setSoRcvBuf(int soRcvBuf) {
        this.soRcvBuf = soRcvBuf;
    }
    
    public int getSoSndBuf() {
        return soSndBuf;
    }
    
    public void setSoSndBuf(int soSndBuf) {
        this.soSndBuf = soSndBuf;
    }
    
    public Heartbeat getHeartbeat() {
        return heartbeat;
    }
    
    public void setHeartbeat(Heartbeat heartbeat) {
        this.heartbeat = heartbeat;
    }
}
