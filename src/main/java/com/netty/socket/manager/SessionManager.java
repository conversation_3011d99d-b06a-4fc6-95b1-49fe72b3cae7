package com.netty.socket.manager;

import com.netty.socket.config.SessionProperties;
import com.netty.socket.model.SessionInfo;
import com.netty.socket.model.SocketMessage;
import io.netty.channel.Channel;
import io.netty.util.AttributeKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 会话管理器
 * 负责管理所有客户端连接会话，提供会话的增删改查、清理等功能
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@Component
public class SessionManager {
    
    private static final Logger logger = LoggerFactory.getLogger(SessionManager.class);
    
    /**
     * 会话ID属性键
     */
    public static final AttributeKey<String> SESSION_ID_KEY = AttributeKey.valueOf("sessionId");
    
    /**
     * 会话存储Map（sessionId -> SessionInfo）
     */
    private final Map<String, SessionInfo> sessions = new ConcurrentHashMap<>();
    
    /**
     * 用户ID到会话ID的映射（userId -> Set<sessionId>）
     */
    private final Map<String, Set<String>> userSessions = new ConcurrentHashMap<>();
    
    /**
     * 端口到会话ID的映射（port -> Set<sessionId>）
     */
    private final Map<Integer, Set<String>> portSessions = new ConcurrentHashMap<>();
    
    /**
     * 会话计数器
     */
    private final AtomicLong sessionCounter = new AtomicLong(0);
    
    @Autowired
    private SessionProperties sessionProperties;
    
    @PostConstruct
    public void init() {
        logger.info("会话管理器初始化完成，配置参数: 超时时间={}分钟, 清理间隔={}分钟, 最大会话数={}", 
            sessionProperties.getTimeout(), sessionProperties.getCleanupInterval(), sessionProperties.getMaxSessions());
    }
    
    /**
     * 添加会话
     * 
     * @param sessionInfo 会话信息
     * @return 是否添加成功
     */
    public boolean addSession(SessionInfo sessionInfo) {
        if (sessionInfo == null || sessionInfo.getSessionId() == null) {
            logger.warn("尝试添加无效的会话信息");
            return false;
        }
        
        // 检查会话数量限制
        if (sessions.size() >= sessionProperties.getMaxSessions()) {
            logger.warn("会话数量已达到最大限制: {}, 拒绝新连接", sessionProperties.getMaxSessions());
            return false;
        }
        
        String sessionId = sessionInfo.getSessionId();
        
        // 添加到主会话Map
        sessions.put(sessionId, sessionInfo);
        
        // 添加到端口映射
        if (sessionInfo.getServerPort() != null) {
            portSessions.computeIfAbsent(sessionInfo.getServerPort(), k -> ConcurrentHashMap.newKeySet())
                .add(sessionId);
        }
        
        // 增加会话计数
        sessionCounter.incrementAndGet();
        
        logger.info("添加会话成功: sessionId={}, 当前会话总数={}", sessionId, sessions.size());
        return true;
    }
    
    /**
     * 移除会话
     * 
     * @param sessionId 会话ID
     * @return 被移除的会话信息
     */
    public SessionInfo removeSession(String sessionId) {
        if (sessionId == null) {
            return null;
        }
        
        SessionInfo sessionInfo = sessions.remove(sessionId);
        
        if (sessionInfo != null) {
            // 从用户会话映射中移除
            if (sessionInfo.getUserId() != null) {
                Set<String> userSessionSet = userSessions.get(sessionInfo.getUserId());
                if (userSessionSet != null) {
                    userSessionSet.remove(sessionId);
                    if (userSessionSet.isEmpty()) {
                        userSessions.remove(sessionInfo.getUserId());
                    }
                }
            }
            
            // 从端口会话映射中移除
            if (sessionInfo.getServerPort() != null) {
                Set<String> portSessionSet = portSessions.get(sessionInfo.getServerPort());
                if (portSessionSet != null) {
                    portSessionSet.remove(sessionId);
                    if (portSessionSet.isEmpty()) {
                        portSessions.remove(sessionInfo.getServerPort());
                    }
                }
            }
            
            logger.info("移除会话成功: sessionId={}, 当前会话总数={}", sessionId, sessions.size());
        }
        
        return sessionInfo;
    }
    
    /**
     * 获取会话信息
     * 
     * @param sessionId 会话ID
     * @return 会话信息
     */
    public SessionInfo getSession(String sessionId) {
        return sessions.get(sessionId);
    }
    
    /**
     * 绑定用户到会话
     * 
     * @param sessionId 会话ID
     * @param userId 用户ID
     */
    public void bindUserToSession(String sessionId, String userId) {
        SessionInfo sessionInfo = sessions.get(sessionId);
        if (sessionInfo != null) {
            // 如果会话之前绑定了其他用户，先解绑
            if (sessionInfo.getUserId() != null) {
                unbindUserFromSession(sessionId);
            }
            
            // 绑定新用户
            sessionInfo.setUserId(userId);
            userSessions.computeIfAbsent(userId, k -> ConcurrentHashMap.newKeySet()).add(sessionId);
            
            logger.info("用户绑定到会话: userId={}, sessionId={}", userId, sessionId);
        }
    }
    
    /**
     * 解绑用户和会话
     * 
     * @param sessionId 会话ID
     */
    public void unbindUserFromSession(String sessionId) {
        SessionInfo sessionInfo = sessions.get(sessionId);
        if (sessionInfo != null && sessionInfo.getUserId() != null) {
            String userId = sessionInfo.getUserId();
            
            // 从用户会话映射中移除
            Set<String> userSessionSet = userSessions.get(userId);
            if (userSessionSet != null) {
                userSessionSet.remove(sessionId);
                if (userSessionSet.isEmpty()) {
                    userSessions.remove(userId);
                }
            }
            
            // 清除会话中的用户ID
            sessionInfo.setUserId(null);
            
            logger.info("用户从会话解绑: userId={}, sessionId={}", userId, sessionId);
        }
    }
    
    /**
     * 获取用户的所有会话
     * 
     * @param userId 用户ID
     * @return 会话信息列表
     */
    public List<SessionInfo> getUserSessions(String userId) {
        Set<String> sessionIds = userSessions.get(userId);
        if (sessionIds == null || sessionIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return sessionIds.stream()
            .map(sessions::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取指定端口的所有会话
     * 
     * @param port 端口号
     * @return 会话信息列表
     */
    public List<SessionInfo> getPortSessions(int port) {
        Set<String> sessionIds = portSessions.get(port);
        if (sessionIds == null || sessionIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return sessionIds.stream()
            .map(sessions::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取所有会话
     * 
     * @return 所有会话信息
     */
    public Collection<SessionInfo> getAllSessions() {
        return new ArrayList<>(sessions.values());
    }
    
    /**
     * 获取会话统计信息
     * 
     * @return 统计信息Map
     */
    public Map<String, Object> getSessionStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalSessions", sessions.size());
        stats.put("totalUsers", userSessions.size());
        stats.put("totalPorts", portSessions.size());
        stats.put("sessionCounter", sessionCounter.get());
        
        // 按状态统计
        Map<SessionInfo.SessionStatus, Long> statusStats = sessions.values().stream()
            .collect(Collectors.groupingBy(SessionInfo::getStatus, Collectors.counting()));
        stats.put("statusStatistics", statusStats);
        
        // 按端口统计
        Map<Integer, Integer> portStats = portSessions.entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().size()));
        stats.put("portStatistics", portStats);
        
        return stats;
    }
    
    /**
     * 向指定会话发送消息
     * 
     * @param sessionId 会话ID
     * @param message 消息
     * @return 是否发送成功
     */
    public boolean sendMessageToSession(String sessionId, SocketMessage message) {
        SessionInfo sessionInfo = sessions.get(sessionId);
        if (sessionInfo == null || !sessionInfo.isActive()) {
            logger.warn("会话不存在或不活跃，无法发送消息: sessionId={}", sessionId);
            return false;
        }
        
        try {
            Channel channel = sessionInfo.getChannel();
            channel.writeAndFlush(message);
            sessionInfo.incrementSentMessageCount();
            sessionInfo.updateLastActiveTime();
            
            logger.debug("向会话发送消息成功: sessionId={}, messageType={}", sessionId, message.getType());
            return true;
        } catch (Exception e) {
            logger.error("向会话发送消息失败: sessionId={}, error={}", sessionId, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 向用户的所有会话发送消息
     * 
     * @param userId 用户ID
     * @param message 消息
     * @return 成功发送的会话数量
     */
    public int sendMessageToUser(String userId, SocketMessage message) {
        List<SessionInfo> userSessionList = getUserSessions(userId);
        int successCount = 0;
        
        for (SessionInfo sessionInfo : userSessionList) {
            if (sendMessageToSession(sessionInfo.getSessionId(), message)) {
                successCount++;
            }
        }
        
        logger.debug("向用户发送消息: userId={}, 总会话数={}, 成功发送数={}", userId, userSessionList.size(), successCount);
        return successCount;
    }
    
    /**
     * 广播消息到所有会话
     * 
     * @param message 消息
     * @return 成功发送的会话数量
     */
    public int broadcastMessage(SocketMessage message) {
        int successCount = 0;
        
        for (SessionInfo sessionInfo : sessions.values()) {
            if (sendMessageToSession(sessionInfo.getSessionId(), message)) {
                successCount++;
            }
        }
        
        logger.info("广播消息: 总会话数={}, 成功发送数={}", sessions.size(), successCount);
        return successCount;
    }
    
    /**
     * 定时清理过期会话
     */
    @Scheduled(fixedRateString = "#{${session.cleanup-interval:10} * 60 * 1000}")
    public void cleanupExpiredSessions() {
        LocalDateTime expireTime = LocalDateTime.now().minusMinutes(sessionProperties.getTimeout());
        List<String> expiredSessionIds = new ArrayList<>();
        
        for (SessionInfo sessionInfo : sessions.values()) {
            if (sessionInfo.getLastActiveTime().isBefore(expireTime) || !sessionInfo.isActive()) {
                expiredSessionIds.add(sessionInfo.getSessionId());
            }
        }
        
        for (String sessionId : expiredSessionIds) {
            SessionInfo removedSession = removeSession(sessionId);
            if (removedSession != null && removedSession.isActive()) {
                try {
                    removedSession.getChannel().close();
                } catch (Exception e) {
                    logger.warn("关闭过期会话连接时发生异常: sessionId={}, error={}", sessionId, e.getMessage());
                }
            }
        }
        
        if (!expiredSessionIds.isEmpty()) {
            logger.info("清理过期会话: 清理数量={}, 当前会话总数={}", expiredSessionIds.size(), sessions.size());
        }
    }
}
