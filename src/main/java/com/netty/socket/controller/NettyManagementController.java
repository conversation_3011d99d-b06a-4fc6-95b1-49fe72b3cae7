package com.netty.socket.controller;

import com.netty.socket.manager.SessionManager;
import com.netty.socket.model.SessionInfo;
import com.netty.socket.model.SocketMessage;
import com.netty.socket.service.MessageRoutingService;
import com.netty.socket.service.NettyServerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Netty管理控制器
 * 提供REST API用于监控和管理Netty服务器
 * 
 * <AUTHOR> Socket Server
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/netty")
public class NettyManagementController {
    
    private static final Logger logger = LoggerFactory.getLogger(NettyManagementController.class);
    
    @Autowired
    private NettyServerService nettyServerService;
    
    @Autowired
    private SessionManager sessionManager;
    
    @Autowired
    private MessageRoutingService messageRoutingService;
    
    /**
     * 获取服务器状态
     * 
     * @return 服务器状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getServerStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 服务器基本信息
        NettyServerService.ServerStats serverStats = nettyServerService.getServerStats();
        status.put("server", serverStats);
        
        // 会话统计信息
        Map<String, Object> sessionStats = sessionManager.getSessionStatistics();
        status.put("sessions", sessionStats);
        
        // 消息队列统计信息
        MessageRoutingService.MessageQueueStats queueStats = messageRoutingService.getQueueStats();
        status.put("messageQueue", queueStats);
        
        // 系统信息
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("timestamp", LocalDateTime.now());
        systemInfo.put("javaVersion", System.getProperty("java.version"));
        systemInfo.put("osName", System.getProperty("os.name"));
        systemInfo.put("availableProcessors", Runtime.getRuntime().availableProcessors());
        systemInfo.put("maxMemory", Runtime.getRuntime().maxMemory());
        systemInfo.put("totalMemory", Runtime.getRuntime().totalMemory());
        systemInfo.put("freeMemory", Runtime.getRuntime().freeMemory());
        status.put("system", systemInfo);
        
        return ResponseEntity.ok(status);
    }
    
    /**
     * 获取所有会话信息
     * 
     * @return 会话信息列表
     */
    @GetMapping("/sessions")
    public ResponseEntity<Collection<SessionInfo>> getAllSessions() {
        Collection<SessionInfo> sessions = sessionManager.getAllSessions();
        return ResponseEntity.ok(sessions);
    }
    
    /**
     * 获取指定会话信息
     * 
     * @param sessionId 会话ID
     * @return 会话信息
     */
    @GetMapping("/sessions/{sessionId}")
    public ResponseEntity<SessionInfo> getSession(@PathVariable String sessionId) {
        SessionInfo sessionInfo = sessionManager.getSession(sessionId);
        if (sessionInfo != null) {
            return ResponseEntity.ok(sessionInfo);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 获取用户的所有会话
     * 
     * @param userId 用户ID
     * @return 用户会话列表
     */
    @GetMapping("/users/{userId}/sessions")
    public ResponseEntity<Collection<SessionInfo>> getUserSessions(@PathVariable String userId) {
        Collection<SessionInfo> sessions = sessionManager.getUserSessions(userId);
        return ResponseEntity.ok(sessions);
    }
    
    /**
     * 向指定会话发送消息
     * 
     * @param sessionId 会话ID
     * @param request 消息请求
     * @return 发送结果
     */
    @PostMapping("/sessions/{sessionId}/messages")
    public ResponseEntity<Map<String, Object>> sendMessageToSession(
            @PathVariable String sessionId,
            @RequestBody MessageRequest request) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            SocketMessage message = new SocketMessage();
            message.setMessageId(UUID.randomUUID().toString());
            message.setSessionId(sessionId);
            message.setType(SocketMessage.MessageType.valueOf(request.getType().toUpperCase()));
            message.setContent(request.getContent());
            message.setData(request.getData());
            message.setSenderId("SYSTEM");
            message.setTimestamp(LocalDateTime.now());
            
            boolean success = messageRoutingService.sendToSession(sessionId, message);
            
            result.put("success", success);
            result.put("messageId", message.getMessageId());
            result.put("timestamp", LocalDateTime.now());
            
            if (success) {
                logger.info("通过API向会话发送消息成功: sessionId={}, messageId={}", sessionId, message.getMessageId());
                return ResponseEntity.ok(result);
            } else {
                result.put("error", "消息发送失败");
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            logger.error("通过API发送消息时发生异常: sessionId={}, error={}", sessionId, e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 向指定用户发送消息
     * 
     * @param userId 用户ID
     * @param request 消息请求
     * @return 发送结果
     */
    @PostMapping("/users/{userId}/messages")
    public ResponseEntity<Map<String, Object>> sendMessageToUser(
            @PathVariable String userId,
            @RequestBody MessageRequest request) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            SocketMessage message = new SocketMessage();
            message.setMessageId(UUID.randomUUID().toString());
            message.setType(SocketMessage.MessageType.valueOf(request.getType().toUpperCase()));
            message.setContent(request.getContent());
            message.setData(request.getData());
            message.setSenderId("SYSTEM");
            message.setReceiverId(userId);
            message.setTimestamp(LocalDateTime.now());
            
            int sentCount = messageRoutingService.sendToUser(userId, message);
            
            result.put("success", sentCount > 0);
            result.put("sentCount", sentCount);
            result.put("messageId", message.getMessageId());
            result.put("timestamp", LocalDateTime.now());
            
            logger.info("通过API向用户发送消息: userId={}, messageId={}, sentCount={}", userId, message.getMessageId(), sentCount);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("通过API向用户发送消息时发生异常: userId={}, error={}", userId, e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 广播消息到所有会话
     * 
     * @param request 消息请求
     * @return 发送结果
     */
    @PostMapping("/broadcast")
    public ResponseEntity<Map<String, Object>> broadcastMessage(@RequestBody MessageRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            SocketMessage message = new SocketMessage();
            message.setMessageId(UUID.randomUUID().toString());
            message.setType(SocketMessage.MessageType.valueOf(request.getType().toUpperCase()));
            message.setContent(request.getContent());
            message.setData(request.getData());
            message.setSenderId("SYSTEM");
            message.setTimestamp(LocalDateTime.now());
            
            int sentCount = messageRoutingService.broadcastMessage(message);
            
            result.put("success", sentCount > 0);
            result.put("sentCount", sentCount);
            result.put("messageId", message.getMessageId());
            result.put("timestamp", LocalDateTime.now());
            
            logger.info("通过API广播消息: messageId={}, sentCount={}", message.getMessageId(), sentCount);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("通过API广播消息时发生异常: error={}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 重启Netty服务器
     * 
     * @return 操作结果
     */
    @PostMapping("/restart")
    public ResponseEntity<Map<String, Object>> restartServer() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("通过API重启Netty服务器");
            nettyServerService.restartServer();
            
            result.put("success", true);
            result.put("message", "服务器重启成功");
            result.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("通过API重启服务器时发生异常: error={}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 消息请求DTO
     */
    public static class MessageRequest {
        private String type;
        private String content;
        private Map<String, Object> data;
        
        // Getter 和 Setter 方法
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        public Map<String, Object> getData() { return data; }
        public void setData(Map<String, Object> data) { this.data = data; }
    }
}
