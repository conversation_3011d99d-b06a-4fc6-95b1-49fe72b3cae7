# Netty Socket 服务器

基于Java 17、Spring Boot 3.4和Netty 4.x的高性能Socket服务框架。

## 项目特性

- **高性能**: 基于Netty NIO框架，支持高并发连接
- **多端口支持**: 支持主端口和扩展端口，实现负载均衡
- **会话管理**: 完整的会话生命周期管理，支持用户绑定
- **消息路由**: 支持点对点消息、广播消息和用户消息
- **心跳检测**: 自动检测连接状态，清理无效连接
- **Spring Boot集成**: 完整的Spring Boot生态支持
- **监控管理**: 提供REST API进行服务监控和管理
- **异常处理**: 完善的异常处理机制和日志记录

## 技术栈

- **Java**: 17
- **Spring Boot**: 3.4.x
- **Netty**: 4.1.104.Final
- **Jackson**: 2.16.0
- **Maven**: 构建工具
- **JUnit 5**: 单元测试

## 项目结构

```
src/
├── main/
│   ├── java/com/netty/socket/
│   │   ├── config/          # 配置类
│   │   ├── controller/      # REST控制器
│   │   ├── exception/       # 异常处理
│   │   ├── handler/         # Netty处理器
│   │   ├── manager/         # 管理器组件
│   │   ├── model/           # 数据模型
│   │   ├── service/         # 服务层
│   │   ├── util/            # 工具类
│   │   └── NettySocketServerApplication.java  # 启动类
│   └── resources/
│       ├── application.yml  # 配置文件
│       └── logback-spring.xml  # 日志配置
└── test/                    # 测试代码
```

## 快速开始

### 1. 环境要求

- JDK 17+
- Maven 3.6+

### 2. 编译项目

```bash
mvn clean compile
```

### 3. 运行测试

```bash
mvn test
```

### 4. 启动应用

```bash
mvn spring-boot:run
```

或者：

```bash
mvn clean package
java -jar target/netty-socket-server-1.0.0.jar
```

### 5. 验证启动

应用启动后，可以通过以下方式验证：

- **健康检查**: http://localhost:8080/actuator/health
- **服务状态**: http://localhost:8080/api/netty/status
- **Socket连接**: 连接到端口9999（默认）

## 配置说明

### 主要配置项

```yaml
# Netty Socket 服务器配置
netty:
  socket:
    primary-port: 9999          # 主端口
    additional-ports:           # 扩展端口列表
      - 10000
      - 10001
    boss-thread-count: 1        # Boss线程数
    worker-thread-count: 8      # Worker线程数
    heartbeat:
      interval: 30              # 心跳间隔（秒）
      timeout: 90               # 心跳超时（秒）
      max-failures: 3           # 最大失败次数

# 会话管理配置
session:
  timeout: 60                   # 会话超时（分钟）
  cleanup-interval: 10          # 清理间隔（分钟）
  max-sessions: 50000           # 最大会话数

# 消息路由配置
message:
  routing:
    queue-size: 1000            # 消息队列大小
    thread-pool-size: 16        # 处理线程池大小
    timeout: 30                 # 消息超时（秒）
```

### 环境配置

- **开发环境**: `spring.profiles.active=dev`
- **生产环境**: `spring.profiles.active=prod`
- **测试环境**: `spring.profiles.active=test`

## API接口

### 服务监控

- `GET /api/netty/status` - 获取服务器状态
- `GET /api/netty/sessions` - 获取所有会话
- `GET /api/netty/sessions/{sessionId}` - 获取指定会话
- `GET /api/netty/users/{userId}/sessions` - 获取用户会话

### 消息发送

- `POST /api/netty/sessions/{sessionId}/messages` - 向会话发送消息
- `POST /api/netty/users/{userId}/messages` - 向用户发送消息
- `POST /api/netty/broadcast` - 广播消息

### 服务管理

- `POST /api/netty/restart` - 重启服务器

## 消息格式

### Socket消息结构

```json
{
  "messageId": "消息唯一标识",
  "sessionId": "会话ID",
  "type": "消息类型",
  "content": "消息内容",
  "data": {},
  "senderId": "发送者ID",
  "receiverId": "接收者ID",
  "timestamp": "2024-01-01T12:00:00",
  "status": "消息状态"
}
```

### 消息类型

- `CONNECT` - 连接建立
- `DISCONNECT` - 断开连接
- `HEARTBEAT` - 心跳消息
- `TEXT` - 文本消息
- `BINARY` - 二进制消息
- `SYSTEM` - 系统消息
- `ERROR` - 错误消息
- `AUTH` - 认证消息
- `ACK` - 确认消息

## 开发指南

### 扩展消息处理

1. 在`MessageProcessingService`中添加新的消息处理逻辑
2. 根据消息内容前缀进行不同的业务处理
3. 支持自定义消息类型和处理流程

### 添加认证机制

1. 在`SocketServerHandler.handleAuthentication()`中实现认证逻辑
2. 可以集成JWT、OAuth2等认证方式
3. 支持用户权限控制和会话绑定

### 数据库集成

1. 添加Spring Data JPA依赖
2. 创建实体类和Repository接口
3. 在`MessageProcessingService`中添加数据持久化逻辑

## 监控和运维

### 日志文件

- `logs/netty-socket-server.log` - 应用日志
- `logs/error.log` - 错误日志
- `logs/netty.log` - Netty相关日志

### 监控指标

- 连接数统计
- 消息处理统计
- 会话状态统计
- 系统资源使用情况

### 性能调优

1. 调整线程池大小
2. 优化消息队列配置
3. 调整心跳检测参数
4. 配置JVM参数

## 常见问题

### Q: 如何修改默认端口？

A: 在`application.yml`中修改`netty.socket.primary-port`配置。

### Q: 如何增加更多端口？

A: 在`netty.socket.additional-ports`列表中添加端口号。

### Q: 如何自定义消息处理逻辑？

A: 在`MessageProcessingService`中添加相应的处理方法。

### Q: 如何集成数据库？

A: 添加相应的Spring Data依赖，创建实体类和Repository。

## 许可证

本项目采用MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题，请通过以下方式联系：

- 项目地址: [GitHub Repository]
- 邮箱: [<EMAIL>]
